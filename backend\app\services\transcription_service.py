import httpx
import logging
import base64
import json
from fastapi import HTTPEx<PERSON>, UploadFile, status
from app.core.config import settings # Import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get OpenAI configuration from environment variables
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") # Removed - Use settings object instead
OPENAI_TRANSCRIPTION_URL = "https://api.openai.com/v1/audio/transcriptions" # Standard Whisper API endpoint

async def transcribe_audio_with_openai(file: UploadFile) -> str:
    """
    Transcribes the given audio file using the OpenAI Whisper API.

    Args:
        file: The audio file uploaded via FastAPI.

    Returns:
        The transcribed text.

    Raises:
        HTTPException: If the API key is missing, the request fails,
                       or the API returns an error.
    """
    # Use settings object to access the API key
    if not settings.OPENAI_API_KEY:
        logger.error("OPENAI_API_KEY environment variable not set.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OpenAI transcription service is not configured.",
        )

    async with httpx.AsyncClient() as client:
        try:
            files = {"file": (file.filename, await file.read(), file.content_type)}
            headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"} # Use settings
            data = {"model": "gpt-4o-mini-transcribe"} # Use requested model

            logger.info(f"Sending OpenAI transcription request for file: {file.filename}")
            response = await client.post(
                OPENAI_TRANSCRIPTION_URL,
                headers=headers,
                files=files,
                data=data,
                timeout=60.0 # Add a timeout
            )
            response.raise_for_status() # Raise exception for 4xx/5xx responses

            response_data = response.json()
            transcribed_text = response_data.get("text")

            if transcribed_text is None:
                logger.error(f"OpenAI transcription failed. Unexpected response format: {response_data}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="OpenAI transcription failed due to unexpected API response.",
                )

            logger.info(f"OpenAI transcription successful for file: {file.filename}")
            return transcribed_text

        except httpx.RequestError as exc:
            logger.error(f"HTTP request failed during OpenAI transcription: {exc}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Could not connect to OpenAI transcription service: {exc}",
            )
        except httpx.HTTPStatusError as exc:
            logger.error(f"OpenAI transcription API returned error status {exc.response.status_code}: {exc.response.text}")
            detail = f"OpenAI transcription failed: API returned status {exc.response.status_code}."
            try:
                # Try to parse more specific error from OpenAI response
                error_detail = exc.response.json().get("error", {}).get("message")
                if error_detail:
                    detail += f" Error: {error_detail}"
            except Exception:
                pass # Ignore if parsing fails
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=detail,
            )
        except Exception as exc:
            logger.exception(f"An unexpected error occurred during OpenAI transcription: {exc}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred during OpenAI transcription: {exc}",
            )


def is_format_compatible_with_gemini(filename: str, content_type: str) -> bool:
    """
    Check if the audio format is compatible with Gemini API.
    Gemini supports: WAV, MP3, AIFF, AAC, OGG Vorbis, FLAC
    """
    if not filename and not content_type:
        return False

    # Check by filename extension
    if filename:
        filename_lower = filename.lower()
        gemini_extensions = ['.wav', '.mp3', '.aiff', '.aac', '.ogg', '.flac']
        if any(filename_lower.endswith(ext) for ext in gemini_extensions):
            return True

    # Check by content type
    if content_type:
        content_type_lower = content_type.lower()
        gemini_types = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/aiff', 'audio/aac', 'audio/ogg', 'audio/flac']
        if any(mime_type in content_type_lower for mime_type in gemini_types):
            return True

    return False


async def transcribe_audio(file: UploadFile) -> str:
    """
    Transcribes the given audio file using OpenAI Whisper API with Gemini fallback.

    This function intelligently chooses the transcription service based on format compatibility:
    - If format is compatible with both: Try OpenAI first, then Gemini fallback
    - If format is only compatible with OpenAI: Use only OpenAI
    - If format is only compatible with Gemini: Use only Gemini

    Args:
        file: The audio file uploaded via FastAPI.

    Returns:
        The transcribed text.

    Raises:
        HTTPException: If transcription fails with all compatible services.
    """
    # Store original file content for potential fallback
    original_position = file.file.tell() if hasattr(file.file, 'tell') else 0

    # Check format compatibility
    gemini_compatible = is_format_compatible_with_gemini(file.filename, file.content_type)

    logger.info(f"File {file.filename} (type: {file.content_type}) - Gemini compatible: {gemini_compatible}")

    try:
        # First attempt: OpenAI Whisper (supports most formats including WebM)
        logger.info(f"Attempting transcription with OpenAI for file: {file.filename}")
        return await transcribe_audio_with_openai(file)

    except HTTPException as openai_error:
        logger.warning(f"OpenAI transcription failed for file {file.filename}: {openai_error.detail}")

        # Only try Gemini fallback if format is compatible
        if not gemini_compatible:
            logger.error(f"File format not compatible with Gemini. Only OpenAI failed for file: {file.filename}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Transcription failed. File format not supported by fallback service. OpenAI error: {openai_error.detail}",
            )

        # Reset file pointer for fallback attempt
        try:
            if hasattr(file.file, 'seek'):
                file.file.seek(original_position)
        except Exception as seek_error:
            logger.warning(f"Could not reset file pointer: {seek_error}")

        # Fallback attempt: Gemini
        try:
            logger.info(f"Attempting fallback transcription with Gemini for file: {file.filename}")
            result = await transcribe_audio_with_gemini(file)
            logger.info(f"Fallback transcription successful with Gemini for file: {file.filename}")
            return result

        except HTTPException as gemini_error:
            logger.error(f"Both OpenAI and Gemini transcription failed for file {file.filename}")
            logger.error(f"OpenAI error: {openai_error.detail}")
            logger.error(f"Gemini error: {gemini_error.detail}")

            # Return a combined error message
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Transcription failed with both services. OpenAI: {openai_error.detail}. Gemini: {gemini_error.detail}",
            )
        except Exception as gemini_unexpected:
            logger.exception(f"Unexpected error during Gemini fallback: {gemini_unexpected}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"OpenAI transcription failed and Gemini fallback encountered unexpected error: {gemini_unexpected}",
            )

    except Exception as unexpected_error:
        logger.exception(f"Unexpected error during transcription: {unexpected_error}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during transcription: {unexpected_error}",
        )


async def transcribe_audio_with_gemini(file: UploadFile) -> str:
    """
    Transcribes the given audio file using the Gemini API as a fallback.

    Args:
        file: The audio file uploaded via FastAPI.

    Returns:
        The transcribed text.

    Raises:
        HTTPException: If the API key is missing, the request fails,
                       or the API returns an error.
    """
    if not settings.GEMINI_API_KEY:
        logger.error("GEMINI_API_KEY environment variable not set.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Gemini transcription service is not configured.",
        )

    try:
        # Read and encode audio file to base64
        audio_content = await file.read()
        audio_base64 = base64.b64encode(audio_content).decode('utf-8')

        # Determine MIME type
        mime_type = file.content_type or "audio/mp3"

        # Prepare Gemini API request
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{settings.GEMINI_MODEL}:generateContent"
        headers = {
            "Content-Type": "application/json"
        }
        params = {
            "key": settings.GEMINI_API_KEY
        }

        # Request body for Gemini API
        request_body = {
            "contents": [{
                "parts": [
                    {"text": "Generate a transcript of the speech. Only return the transcribed text, no additional commentary."},
                    {
                        "inline_data": {
                            "mime_type": mime_type,
                            "data": audio_base64
                        }
                    }
                ]
            }]
        }

        logger.info(f"Sending Gemini transcription request for file: {file.filename}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                headers=headers,
                params=params,
                json=request_body,
                timeout=120.0  # Longer timeout for Gemini
            )
            response.raise_for_status()

            response_data = response.json()

            # Extract transcribed text from Gemini response
            try:
                transcribed_text = response_data["candidates"][0]["content"]["parts"][0]["text"]
                if not transcribed_text or not transcribed_text.strip():
                    raise ValueError("Empty transcription received")

                logger.info(f"Gemini transcription successful for file: {file.filename}")
                return transcribed_text.strip()

            except (KeyError, IndexError, ValueError) as e:
                logger.error(f"Failed to parse Gemini response: {response_data}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to parse Gemini transcription response: {e}",
                )

    except httpx.RequestError as exc:
        logger.error(f"HTTP request failed during Gemini transcription: {exc}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Could not connect to Gemini transcription service: {exc}",
        )
    except httpx.HTTPStatusError as exc:
        logger.error(f"Gemini API returned error status {exc.response.status_code}: {exc.response.text}")
        detail = f"Gemini transcription failed: API returned status {exc.response.status_code}."
        try:
            # Try to parse more specific error from Gemini response
            error_detail = exc.response.json().get("error", {}).get("message")
            if error_detail:
                detail += f" Error: {error_detail}"
        except Exception:
            pass  # Ignore if parsing fails
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
        )
    except Exception as exc:
        logger.exception(f"An unexpected error occurred during Gemini transcription: {exc}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during Gemini transcription: {exc}",
        )